# Barber Brothers Legacy - Environment Variables Example
# Copy this file to .env and fill in your actual values

# ================================
# FIREBASE CONFIGURATION
# ================================
# Get these values from Firebase Console > Project Settings > General > Your apps
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key-here
REACT_APP_FIREBASE_AUTH_DOMAIN=barber-brothers-legacy.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=barber-brothers-legacy
REACT_APP_FIREBASE_STORAGE_BUCKET=barber-brothers-legacy.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
REACT_APP_FIREBASE_APP_ID=your-firebase-app-id
REACT_APP_FIREBASE_MEASUREMENT_ID=your-measurement-id-for-analytics

# Firebase Development Settings
REACT_APP_USE_FIREBASE_EMULATORS=false
NODE_ENV=production

# ================================
# TWILIO CONFIGURATION
# ================================
# Get these values from your Twilio Console (https://console.twilio.com)
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# Option 2: Use a Messaging Service (Recommended)
TWILIO_MESSAGING_SERVICE_SID=MGb9c22980673824cbf6e84b6ce814da99

# Barber's phone number (where notifications will be sent)
BARBER_PHONE_NUMBER=your_barber_phone_number_here

# ================================
# GOOGLE OAUTH CONFIGURATION
# ================================
# Get these from Google Cloud Console > APIs & Services > Credentials
GOOGLE_CLIENT_ID=************-06f189ukl4tg3oi4d499m4dnf015pjl1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://www.barberbrotherz.com/auth-callback.html
GOOGLE_CALLBACK_URL=https://www.barberbrotherz.com/auth/callback

# ================================
# COMMUNITY HUB BACKEND
# ================================
# MongoDB connection string
MONGODB_URI=mongodb+srv://username:<EMAIL>/barber-brothers-community

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret-key-here
REFRESH_TOKEN_EXPIRES_IN=30d

# Server Configuration
PORT=5000
FRONTEND_URL=https://www.barberbrotherz.com

# ================================
# SECURITY SETTINGS
# ================================
# CORS origins (comma-separated)
CORS_ORIGINS=https://www.barberbrotherz.com,https://barberbrotherz.com

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100